<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="800px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="140px"
      v-loading="formLoading"
    >
      <!-- 基本信息（只读） -->
      <el-row :gutter="20" v-if="formType !== 'create'">
        <el-col :span="12">
          <el-form-item label="数据编号">
            <el-input v-model="formData.dataNumber" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数据名称">
            <el-input v-model="formData.dataName" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="formType !== 'create'">
        <el-col :span="12">
          <el-form-item label="存证单位">
            <el-input v-model="formData.certificationUnit" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数据分类">
            <el-input v-model="formData.dataClassification" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 审核信息 -->
      <el-form-item label="审查时间" prop="reviewDate" v-if="formType === 'review'">
        <el-date-picker
          v-model="formData.reviewDate"
          type="datetime"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="选择审查时间"
          style="width: 100%"
          disabled
        />
      </el-form-item>

      <el-form-item label="审查结果" prop="reviewResults" v-if="formType === 'review'">
        <el-input
          v-model="formData.reviewResults"
          type="textarea"
          :rows="3"
          placeholder="审查结果将根据合规性检查自动生成"
          disabled
        />
      </el-form-item>

      <!-- 合规性检查 -->
      <el-row :gutter="20" v-if="formType === 'review'">
        <el-col :span="12">
          <el-form-item label="权益主体合规性" prop="reviewDataSubjectComp">
            <el-radio-group v-model="formData.reviewDataSubjectComp" :disabled="formType === 'view'">
              <el-radio :label="1">合规</el-radio>
              <el-radio :label="0">不合规</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数据来源合规性" prop="reviewDataSourceComp">
            <el-radio-group v-model="formData.reviewDataSourceComp" :disabled="formType === 'view'">
              <el-radio :label="1">合规</el-radio>
              <el-radio :label="0">不合规</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="formType === 'review'">
        <el-col :span="12">
          <el-form-item label="数据处理合规性" prop="reviewDataProcessComp">
            <el-radio-group v-model="formData.reviewDataProcessComp" :disabled="formType === 'view'">
              <el-radio :label="1">合规</el-radio>
              <el-radio :label="0">不合规</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数据内容合规性" prop="reviewDataContentComp">
            <el-radio-group v-model="formData.reviewDataContentComp" :disabled="formType === 'view'">
              <el-radio :label="1">合规</el-radio>
              <el-radio :label="0">不合规</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="审核状态" prop="statusCd" v-if="formType === 'review'">
        <el-radio-group v-model="formData.statusCd" disabled>
          <el-radio
            v-for="dict in getStrDictOptions(DICT_TYPE.DATA_EVIDENCE_REVIEW_NODE)"
            :key="dict.value"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注，额外的审核意见可以填到这里。"
          :disabled="formType === 'view'"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading" v-if="formType === 'review'">
        提交
      </el-button>
      <el-button @click="dialogVisible = false">{{ formType === 'view' ? '关闭' : '取消' }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { CertificateReviewApi, CertificateReviewSaveReqVO } from '@/api/business/certificatereview'
import { CertificateInfoVO, CertificateReviewVO } from '@/api/business/certificateinfo'
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'

/** 存证信息审核表单 */
defineOptions({ name: 'CertificateReviewForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：review - 审核；view - 查看
const formData = ref({
  // 原始数据存证信息
  id: undefined as number | undefined, // 数据存证信息ID
  dataNumber: undefined as string | undefined,
  dataName: undefined as string | undefined,
  certificationUnit: undefined as string | undefined,
  dataClassification: undefined as string | undefined,
  // 审核相关信息
  reviewId: undefined as number | undefined, // 审核记录ID
  reviewDataId: undefined as number | undefined,
  reviewOrgId: undefined as number | undefined,
  reviewDate: undefined as string | undefined, // 表单中保持字符串格式，提交时转换
  reviewResults: undefined as string | undefined,
  reviewDataSubjectComp: undefined as number | undefined,
  reviewDataSourceComp: undefined as number | undefined,
  reviewDataProcessComp: undefined as number | undefined,
  reviewDataContentComp: undefined as number | undefined,
  statusCd: undefined as string | undefined,
  remark: undefined as string | undefined
})
const formRules = reactive({
  reviewResults: [{ required: true, message: '审查结果不能为空', trigger: 'blur' }],
  reviewDataSubjectComp: [{ required: true, message: '请选择权益主体合规性', trigger: 'change' }],
  reviewDataSourceComp: [{ required: true, message: '请选择数据来源合规性', trigger: 'change' }],
  reviewDataProcessComp: [{ required: true, message: '请选择数据处理合规性', trigger: 'change' }],
  reviewDataContentComp: [{ required: true, message: '请选择数据内容合规性', trigger: 'change' }],
  statusCd: [{ required: true, message: '请选择审核状态', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

/** 获取最新的审核状态 */
const getLatestReviewStatus = (certificateReviews: CertificateReviewVO[]): string => {
  if (!certificateReviews || certificateReviews.length === 0) {
    return '' // 如果没有审核记录，返回空字符串
  }

  // 按创建时间降序排序，获取最新的审核记录
  const sortedReviews = certificateReviews.sort((a, b) => {
    return new Date(b.createTime).getTime() - new Date(a.createTime).getTime()
  })

  return sortedReviews[0].statusCd || ''
}

/** 监听合规性字段变化，自动设置审核状态和审核结果 */
const updateReviewStatus = () => {
  const { reviewDataSubjectComp, reviewDataSourceComp, reviewDataProcessComp, reviewDataContentComp } = formData.value

  // 只有当所有合规性字段都有值时才进行判断
  if (reviewDataSubjectComp !== undefined && reviewDataSourceComp !== undefined &&
      reviewDataProcessComp !== undefined && reviewDataContentComp !== undefined) {

    // 获取字典选项
    const dictOptions = getStrDictOptions(DICT_TYPE.DATA_EVIDENCE_REVIEW_NODE)

    // 查找审核通过和审核拒绝的字典值
    const approvedOption = dictOptions.find(option =>
      option.label.includes('通过') || option.label.includes('审核通过') || option.label.includes('APPROVED')
    )
    const rejectedOption = dictOptions.find(option =>
      option.label.includes('拒绝') || option.label.includes('审核拒绝') || option.label.includes('不通过') || option.label.includes('REJECTED')
    )

    // 生成详细的审核结果内容
    const complianceResults = [
      `权益主体合规性：${reviewDataSubjectComp === 1 ? '合规' : '不合规'}`,
      `数据来源合规性：${reviewDataSourceComp === 1 ? '合规' : '不合规'}`,
      `数据处理合规性：${reviewDataProcessComp === 1 ? '合规' : '不合规'}`,
      `数据内容合规性：${reviewDataContentComp === 1 ? '合规' : '不合规'}`
    ]

    // 统计不合规项目
    const nonCompliantItems: string[] = []
    if (reviewDataSubjectComp === 0) nonCompliantItems.push('权益主体合规性')
    if (reviewDataSourceComp === 0) nonCompliantItems.push('数据来源合规性')
    if (reviewDataProcessComp === 0) nonCompliantItems.push('数据处理合规性')
    if (reviewDataContentComp === 0) nonCompliantItems.push('数据内容合规性')

    // 生成审核结果文本
    let reviewResultText = complianceResults.join('；') + '。\n\n'

    // 如果4个合规性都为1（合规），则审核状态为审核通过，否则为审核拒绝
    if (reviewDataSubjectComp === 1 && reviewDataSourceComp === 1 &&
        reviewDataProcessComp === 1 && reviewDataContentComp === 1) {
      formData.value.statusCd = approvedOption?.value || '20' // 审核通过
      reviewResultText += '经审核，所有合规性检查均符合要求，审核通过。'
    } else {
      formData.value.statusCd = rejectedOption?.value || '30' // 审核拒绝
      reviewResultText += `经审核，发现以下项目不合规：${nonCompliantItems.join('、')}，审核拒绝。`
    }

    // 设置审核结果
    formData.value.reviewResults = reviewResultText
  }
}

/** 监听合规性字段变化 */
watch([
  () => formData.value.reviewDataSubjectComp,
  () => formData.value.reviewDataSourceComp,
  () => formData.value.reviewDataProcessComp,
  () => formData.value.reviewDataContentComp
], () => {
  if (formType.value === 'review') {
    updateReviewStatus()
  }
}, { deep: true })

/** 打开弹窗 */
const open = async (type: string, dataId?: number, data?: CertificateInfoVO, reviewId?: number) => {
  dialogVisible.value = true
  formType.value = type

  if (type === 'review') {
    dialogTitle.value = reviewId ? '更新数据存证审核' : '数据存证审核'
  } else if (type === 'view') {
    dialogTitle.value = '查看审核详情'
  }

  resetForm()

  // 如果有数据，直接使用传入的数据
  if (data) {
    // 复制数据存证信息到表单
    formData.value = {
      ...formData.value,
      ...data,
      // 设置审核相关字段
      reviewId: reviewId, // 如果传入了reviewId，则为更新操作
      reviewDataId: data.id, // 将数据存证ID设置为reviewDataId
      reviewOrgId: 1, // 默认审查机构ID，可以根据实际情况调整
      statusCd: getLatestReviewStatus(data.certificateReviews || []), // 从审核记录中获取最新状态
    }

    // 如果是更新操作，尝试从certificateReviews中找到对应的审核记录
    if (reviewId && data.certificateReviews && data.certificateReviews.length > 0) {
      const reviewRecord = data.certificateReviews.find(review => review.id === reviewId)
      if (reviewRecord) {
        // 填充审核记录数据
        formData.value.reviewDate = formatDate(new Date(reviewRecord.reviewDate))
        formData.value.reviewResults = reviewRecord.reviewResults
        formData.value.reviewDataSubjectComp = reviewRecord.reviewDataSubjectComp
        formData.value.reviewDataSourceComp = reviewRecord.reviewDataSourceComp
        formData.value.reviewDataProcessComp = reviewRecord.reviewDataProcessComp
        formData.value.reviewDataContentComp = reviewRecord.reviewDataContentComp
        formData.value.statusCd = reviewRecord.statusCd
        formData.value.remark = reviewRecord.remark
      }
    }

    // 无论是新建还是更新，在审核模式下都设置审查时间为当前系统时间
    if (type === 'review') {
      formData.value.reviewDate = formatDate(new Date())
    }

    // 如果是审核模式，初始化审核结果
    if (type === 'review') {
      // 延迟执行，确保数据已经设置完成
      nextTick(() => {
        updateReviewStatus()
      })
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    if (formType.value === 'review') {
      // 构造审核数据，只提交审核相关字段
      const reviewData: CertificateReviewSaveReqVO = {
        id: formData.value.reviewId, // 如果有reviewId则为更新，否则为创建
        reviewDataId: formData.value.reviewDataId!,
        reviewOrgId: formData.value.reviewOrgId!,
        reviewDate: formData.value.reviewDate ? new Date(formData.value.reviewDate).getTime() : undefined,
        reviewResults: formData.value.reviewResults,
        reviewDataSubjectComp: formData.value.reviewDataSubjectComp,
        reviewDataSourceComp: formData.value.reviewDataSourceComp,
        reviewDataProcessComp: formData.value.reviewDataProcessComp,
        reviewDataContentComp: formData.value.reviewDataContentComp,
        statusCd: formData.value.statusCd,
        remark: formData.value.remark
      }

      // 根据是否有reviewId来决定创建还是更新
      if (formData.value.reviewId) {
        await CertificateReviewApi.updateCertificateReview(reviewData)
        message.success('审核更新成功')
      } else {
        await CertificateReviewApi.createCertificateReview(reviewData)
        message.success('审核提交成功')
      }
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    // 原始数据存证信息
    id: undefined as number | undefined,
    dataNumber: undefined as string | undefined,
    dataName: undefined as string | undefined,
    certificationUnit: undefined as string | undefined,
    dataClassification: undefined as string | undefined,
    // 审核相关信息
    reviewId: undefined as number | undefined,
    reviewDataId: undefined as number | undefined,
    reviewOrgId: undefined as number | undefined,
    reviewDate: undefined as string | undefined, // 表单中保持字符串格式，提交时转换
    reviewResults: undefined as string | undefined,
    reviewDataSubjectComp: undefined as number | undefined,
    reviewDataSourceComp: undefined as number | undefined,
    reviewDataProcessComp: undefined as number | undefined,
    reviewDataContentComp: undefined as number | undefined,
    statusCd: undefined as string | undefined,
    remark: undefined as string | undefined
  }
  formRef.value?.resetFields()
}
</script>
